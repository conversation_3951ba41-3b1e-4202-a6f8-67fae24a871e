{"version": 3, "file": "base64url.js", "sourceRoot": "", "sources": ["../../../src/lib/base64url.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;;GAGG;AACH,MAAM,YAAY,GAAG,kEAAkE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;AAEjG;;;GAGG;AACH,MAAM,gBAAgB,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;AAE7C;;;GAGG;AACH,MAAM,cAAc,GAAG,CAAC,GAAG,EAAE;IAC3B,MAAM,OAAO,GAAa,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;IAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;KAChB;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QACnD,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;KAChD;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QAC/C,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;KAC3C;IAED,OAAO,OAAO,CAAA;AAChB,CAAC,CAAC,EAAE,CAAA;AAEJ;;;;;;GAMG;AACH,MAAM,UAAU,eAAe,CAC7B,IAAmB,EACnB,KAA4C,EAC5C,IAA4B;IAE5B,IAAI,IAAI,KAAK,IAAI,EAAE;QACjB,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAA;QACvC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAA;QAErB,OAAO,KAAK,CAAC,UAAU,IAAI,CAAC,EAAE;YAC5B,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA;YACxD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAA;YACvB,KAAK,CAAC,UAAU,IAAI,CAAC,CAAA;SACtB;KACF;SAAM,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE;QAC/B,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAA;QACnD,KAAK,CAAC,UAAU,GAAG,CAAC,CAAA;QAEpB,OAAO,KAAK,CAAC,UAAU,IAAI,CAAC,EAAE;YAC5B,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA;YACxD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAA;YACvB,KAAK,CAAC,UAAU,IAAI,CAAC,CAAA;SACtB;KACF;AACH,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,iBAAiB,CAC/B,QAAgB,EAChB,KAA4C,EAC5C,IAA4B;IAE5B,MAAM,IAAI,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAA;IAErC,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE;QACb,6BAA6B;QAC7B,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAA;QACvC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAA;QAErB,OAAO,KAAK,CAAC,UAAU,IAAI,CAAC,EAAE;YAC5B,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAA;YACpD,KAAK,CAAC,UAAU,IAAI,CAAC,CAAA;SACtB;KACF;SAAM,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE;QACtB,mCAAmC;QACnC,OAAM;KACP;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,iCAAiC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;KACnF;AACH,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,iBAAiB,CAAC,GAAW;IAC3C,MAAM,MAAM,GAAa,EAAE,CAAA;IAE3B,MAAM,OAAO,GAAG,CAAC,IAAY,EAAE,EAAE;QAC/B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACnB,CAAC,CAAA;IAED,MAAM,KAAK,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAA;IAEzC,YAAY,CAAC,GAAG,EAAE,CAAC,IAAY,EAAE,EAAE;QACjC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;IACvC,CAAC,CAAC,CAAA;IAEF,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;IAErC,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;AACxB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,mBAAmB,CAAC,GAAW;IAC7C,MAAM,IAAI,GAAa,EAAE,CAAA;IAEzB,MAAM,QAAQ,GAAG,CAAC,SAAiB,EAAE,EAAE;QACrC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAA;IAC5C,CAAC,CAAA;IAED,MAAM,SAAS,GAAG;QAChB,OAAO,EAAE,CAAC;QACV,SAAS,EAAE,CAAC;KACb,CAAA;IAED,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAA;IAE5C,MAAM,QAAQ,GAAG,CAAC,IAAY,EAAE,EAAE;QAChC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAA;IAC3C,CAAC,CAAA;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QACtC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;KACzD;IAED,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;AACtB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,eAAe,CAAC,SAAiB,EAAE,IAA4B;IAC7E,IAAI,SAAS,IAAI,IAAI,EAAE;QACrB,IAAI,CAAC,SAAS,CAAC,CAAA;QACf,OAAM;KACP;SAAM,IAAI,SAAS,IAAI,KAAK,EAAE;QAC7B,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,CAAA;QAC7B,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAA;QAC/B,OAAM;KACP;SAAM,IAAI,SAAS,IAAI,MAAM,EAAE;QAC9B,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAA;QAC9B,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAA;QACtC,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAA;QAC/B,OAAM;KACP;SAAM,IAAI,SAAS,IAAI,QAAQ,EAAE;QAChC,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAA;QAC9B,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAA;QACvC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAA;QACtC,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAA;QAC/B,OAAM;KACP;IAED,MAAM,IAAI,KAAK,CAAC,mCAAmC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;AAC9E,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,YAAY,CAAC,GAAW,EAAE,IAA4B;IACpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QACtC,IAAI,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QAEjC,IAAI,SAAS,GAAG,MAAM,IAAI,SAAS,IAAI,MAAM,EAAE;YAC7C,uEAAuE;YACvE,sEAAsE;YACtE,2CAA2C;YAC3C,MAAM,aAAa,GAAG,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAA;YAC7D,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAA;YAC9D,SAAS,GAAG,CAAC,YAAY,GAAG,aAAa,CAAC,GAAG,OAAO,CAAA;YACpD,CAAC,IAAI,CAAC,CAAA;SACP;QAED,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;KACjC;AACH,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,cAAc,CAC5B,IAAY,EACZ,KAA6C,EAC7C,IAAiC;IAEjC,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE;QACvB,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,IAAI,CAAC,IAAI,CAAC,CAAA;YACV,OAAM;SACP;QAED,uDAAuD;QACvD,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,UAAU,IAAI,CAAC,EAAE;YACxD,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC1C,KAAK,CAAC,OAAO,GAAG,UAAU,CAAA;gBAC1B,MAAK;aACN;SACF;QAED,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE;YACvB,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAA;SAC5B;aAAM,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE;YAC9B,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAA;SAC5B;aAAM,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE;YAC9B,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,CAAC,CAAA;SAC3B;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;SAC1C;QAED,KAAK,CAAC,OAAO,IAAI,CAAC,CAAA;KACnB;SAAM,IAAI,KAAK,CAAC,OAAO,GAAG,CAAC,EAAE;QAC5B,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;SAC1C;QAED,KAAK,CAAC,SAAS,GAAG,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAA;QACtD,KAAK,CAAC,OAAO,IAAI,CAAC,CAAA;QAElB,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE;YACvB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;SACtB;KACF;AACH,CAAC;AAED;;GAEG;AAEH,MAAM,UAAU,qBAAqB,CAAC,GAAW;IAC/C,MAAM,MAAM,GAAa,EAAE,CAAA;IAC3B,MAAM,KAAK,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAA;IAEzC,MAAM,MAAM,GAAG,CAAC,IAAY,EAAE,EAAE;QAC9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACnB,CAAC,CAAA;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QACtC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;KACpD;IAED,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAA;AAC/B,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,GAAW;IAC5C,MAAM,MAAM,GAAa,EAAE,CAAA;IAC3B,YAAY,CAAC,GAAG,EAAE,CAAC,IAAY,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;IACtD,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAA;AAC/B,CAAC"}