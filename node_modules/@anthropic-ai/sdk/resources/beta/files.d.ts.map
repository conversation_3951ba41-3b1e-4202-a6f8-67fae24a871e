{"version": 3, "file": "files.d.ts", "sourceRoot": "", "sources": ["../../src/resources/beta/files.ts"], "names": [], "mappings": "OAEO,EAAE,WAAW,EAAE;OACf,KAAK,OAAO;OACZ,EAAE,UAAU,EAAE;OACd,EAAE,IAAI,EAAE,KAAK,UAAU,EAAE,WAAW,EAAE;OACtC,EAAE,KAAK,UAAU,EAAE;OAEnB,EAAE,cAAc,EAAE;AAIzB,qBAAa,KAAM,SAAQ,WAAW;IACpC;;;;;;;;;;OAUG;IACH,IAAI,CACF,MAAM,GAAE,cAAc,GAAG,IAAI,GAAG,SAAc,EAC9C,OAAO,CAAC,EAAE,cAAc,GACvB,WAAW,CAAC,gBAAgB,EAAE,YAAY,CAAC;IAY9C;;;;;;;;;OASG;IACH,MAAM,CACJ,MAAM,EAAE,MAAM,EACd,MAAM,GAAE,gBAAgB,GAAG,IAAI,GAAG,SAAc,EAChD,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,WAAW,CAAC;IAW1B;;;;;;;;;;;;OAYG;IACH,QAAQ,CACN,MAAM,EAAE,MAAM,EACd,MAAM,GAAE,kBAAkB,GAAG,IAAI,GAAG,SAAc,EAClD,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,QAAQ,CAAC;IAevB;;;;;;;;OAQG;IACH,gBAAgB,CACd,MAAM,EAAE,MAAM,EACd,MAAM,GAAE,0BAA0B,GAAG,IAAI,GAAG,SAAc,EAC1D,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,YAAY,CAAC;IAW3B;;;;;;;;;OASG;IACH,MAAM,CAAC,MAAM,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC;CAiBrF;AAED,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;AAElD,MAAM,WAAW,WAAW;IAC1B;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;;;OAIG;IACH,IAAI,CAAC,EAAE,cAAc,CAAC;CACvB;AAED,MAAM,WAAW,YAAY;IAC3B;;;;OAIG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;;OAIG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,YAAY,CAAC,EAAE,OAAO,CAAC;CACxB;AAED,MAAM,WAAW,cAAe,SAAQ,UAAU;IAChD;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,MAAM,WAAW,kBAAkB;IACjC;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,MAAM,WAAW,0BAA0B;IACzC;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,IAAI,EAAE,UAAU,CAAC;IAEjB;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,MAAM,CAAC,OAAO,WAAW,KAAK,CAAC;IAC7B,OAAO,EACL,KAAK,WAAW,IAAI,WAAW,EAC/B,KAAK,YAAY,IAAI,YAAY,EACjC,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,cAAc,IAAI,cAAc,EACrC,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,0BAA0B,IAAI,0BAA0B,EAC7D,KAAK,gBAAgB,IAAI,gBAAgB,GAC1C,CAAC;CACH"}