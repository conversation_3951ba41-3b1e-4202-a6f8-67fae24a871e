{"version": 3, "file": "uploads.js", "sourceRoot": "", "sources": ["../src/internal/uploads.ts"], "names": [], "mappings": ";;;AA0CA,4BAOC;AAED,0BAcC;AA9DD,sCAA6C;AAUtC,MAAM,gBAAgB,GAAG,GAAG,EAAE;IACnC,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE,CAAC;QAChC,MAAM,EAAE,OAAO,EAAE,GAAG,UAAiB,CAAC;QACtC,MAAM,SAAS,GACb,OAAO,OAAO,EAAE,QAAQ,EAAE,IAAI,KAAK,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QACjG,MAAM,IAAI,KAAK,CACb,wEAAwE;YACtE,CAAC,SAAS,CAAC,CAAC;gBACV,4FAA4F;gBAC9F,CAAC,CAAC,EAAE,CAAC,CACR,CAAC;IACJ,CAAC;AACH,CAAC,CAAC;AAZW,QAAA,gBAAgB,oBAY3B;AAaF;;;GAGG;AACH,SAAgB,QAAQ,CACtB,QAAoB,EACpB,QAA4B,EAC5B,OAAyB;IAEzB,IAAA,wBAAgB,GAAE,CAAC;IACnB,OAAO,IAAI,IAAI,CAAC,QAAe,EAAE,QAAQ,IAAI,cAAc,EAAE,OAAO,CAAC,CAAC;AACxE,CAAC;AAED,SAAgB,OAAO,CAAC,KAAU;IAChC,OAAO,CACL,CACE,CAAC,OAAO,KAAK,KAAK,QAAQ;QACxB,KAAK,KAAK,IAAI;QACd,CAAC,CAAC,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACpD,CAAC,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClD,CAAC,UAAU,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACjE,CAAC,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3D,EAAE,CACH;SACE,KAAK,CAAC,OAAO,CAAC;SACd,GAAG,EAAE,IAAI,SAAS,CACtB,CAAC;AACJ,CAAC;AAEM,MAAM,eAAe,GAAG,CAAC,KAAU,EAA+B,EAAE,CACzE,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,UAAU,CAAC;AADrF,QAAA,eAAe,mBACsE;AAElG;;;GAGG;AACI,MAAM,gCAAgC,GAAG,KAAK,EACnD,IAAoB,EACpB,KAA4B,EACH,EAAE;IAC3B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;QAAE,OAAO,IAAI,CAAC;IAEhD,OAAO,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,MAAM,IAAA,kBAAU,EAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;AAC/D,CAAC,CAAC;AAPW,QAAA,gCAAgC,oCAO3C;AAIK,MAAM,2BAA2B,GAAG,KAAK,EAC9C,IAAiC,EACjC,KAA4B,EACH,EAAE;IAC3B,OAAO,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,MAAM,IAAA,kBAAU,EAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;AAC/D,CAAC,CAAC;AALW,QAAA,2BAA2B,+BAKtC;AAEF,MAAM,mBAAmB,GAAG,IAAI,OAAO,EAA2B,CAAC;AAEnE;;;;;GAKG;AACH,SAAS,gBAAgB,CAAC,WAAkC;IAC1D,MAAM,KAAK,GAAU,OAAO,WAAW,KAAK,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAE,WAAmB,CAAC,KAAK,CAAC;IAClG,MAAM,MAAM,GAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC9C,IAAI,MAAM;QAAE,OAAO,MAAM,CAAC;IAC1B,MAAM,OAAO,GAAG,CAAC,KAAK,IAAI,EAAE;QAC1B,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,CACpB,UAAU,IAAI,KAAK,CAAC,CAAC;gBACnB,KAAK,CAAC,QAAQ;gBAChB,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAoB,CAAC;YAC5D,MAAM,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAC;YAC5B,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,wBAAwB;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC,CAAC,EAAE,CAAC;IACL,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACxC,OAAO,OAAO,CAAC;AACjB,CAAC;AAEM,MAAM,UAAU,GAAG,KAAK,EAC7B,IAAmB,EACnB,KAA4B,EACT,EAAE;IACrB,IAAI,CAAC,CAAC,MAAM,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QACrC,MAAM,IAAI,SAAS,CACjB,mGAAmG,CACpG,CAAC;IACJ,CAAC;IACD,MAAM,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAC;IAC5B,MAAM,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IACpG,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAZW,QAAA,UAAU,cAYrB;AAEF,yEAAyE;AACzE,yEAAyE;AACzE,MAAM,WAAW,GAAG,CAAC,KAAa,EAAiB,EAAE,CAAC,KAAK,YAAY,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC;AAE/F,MAAM,YAAY,GAAG,CAAC,KAAc,EAAE,EAAE,CACtC,OAAO,KAAK,KAAK,QAAQ;IACzB,KAAK,KAAK,IAAI;IACd,CAAC,KAAK,YAAY,QAAQ,IAAI,IAAA,uBAAe,EAAC,KAAK,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AAE9E,MAAM,kBAAkB,GAAG,CAAC,KAAc,EAAW,EAAE;IACrD,IAAI,YAAY,CAAC,KAAK,CAAC;QAAE,OAAO,IAAI,CAAC;IACrC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAChE,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QACvC,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC;YACtB,IAAI,kBAAkB,CAAE,KAAa,CAAC,CAAC,CAAC,CAAC;gBAAE,OAAO,IAAI,CAAC;QACzD,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,KAAK,EAAE,IAAc,EAAE,GAAW,EAAE,KAAc,EAAiB,EAAE;IACxF,IAAI,KAAK,KAAK,SAAS;QAAE,OAAO;IAChC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,MAAM,IAAI,SAAS,CACjB,sBAAsB,GAAG,6DAA6D,CACvF,CAAC;IACJ,CAAC;IAED,yCAAyC;IACzC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;QACzF,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAClC,CAAC;SAAM,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QACrC,IAAI,OAAO,GAAG,EAAqB,CAAC;QACpC,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QACtD,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;IAC5E,CAAC;SAAM,IAAI,IAAA,uBAAe,EAAC,KAAK,CAAC,EAAE,CAAC;QAClC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAA,0BAAkB,EAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACrG,CAAC;SAAM,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAC5E,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QAChC,MAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IACjF,CAAC;SAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QACrC,MAAM,OAAO,CAAC,GAAG,CACf,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,CAAC,CACzF,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,SAAS,CACjB,wGAAwG,KAAK,UAAU,CACxH,CAAC;IACJ,CAAC;AACH,CAAC,CAAC"}