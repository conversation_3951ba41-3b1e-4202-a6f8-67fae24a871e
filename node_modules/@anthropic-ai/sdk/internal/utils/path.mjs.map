{"version": 3, "file": "path.mjs", "sourceRoot": "", "sources": ["../../src/internal/utils/path.ts"], "names": [], "mappings": "OAAO,EAAE,cAAc,EAAE;AAEzB;;;;;;;GAOG;AACH,MAAM,UAAU,aAAa,CAAC,GAAW;IACvC,OAAO,GAAG,CAAC,OAAO,CAAC,kCAAkC,EAAE,kBAAkB,CAAC,CAAC;AAC7E,CAAC;AAED,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,WAAW,GAAG,aAAa,EAAE,EAAE,CACnE,SAAS,IAAI,CAAC,OAA0B,EAAE,GAAG,MAA0B;IACrE,mDAAmD;IACnD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,OAAO,CAAC,CAAC,CAAE,CAAC;IAE7C,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE;QACjE,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YAC9B,QAAQ,GAAG,IAAI,CAAC;QAClB,CAAC;QACD,OAAO,CACL,aAAa;YACb,YAAY;YACZ,CAAC,KAAK,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CACtG,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC;IAC3C,MAAM,eAAe,GAAG,EAAE,CAAC;IAC3B,MAAM,qBAAqB,GAAG,oCAAoC,CAAC;IACnE,IAAI,KAAK,CAAC;IAEV,4BAA4B;IAC5B,OAAO,CAAC,KAAK,GAAG,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QAC/D,eAAe,CAAC,IAAI,CAAC;YACnB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM;SACxB,CAAC,CAAC;IACL,CAAC;IAED,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC/B,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,MAAM,SAAS,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;YACxD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC;YACnD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC1C,OAAO,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC;YACzC,OAAO,GAAG,GAAG,MAAM,GAAG,MAAM,CAAC;QAC/B,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,MAAM,IAAI,cAAc,CACtB,0DAA0D,IAAI,KAAK,SAAS,EAAE,CAC/E,CAAC;IACJ,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEJ;;GAEG;AACH,MAAM,CAAC,MAAM,IAAI,GAAG,qBAAqB,CAAC,aAAa,CAAC,CAAC"}