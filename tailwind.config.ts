import type { Config } from "tailwindcss";

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        flame: '#FF4500',
        ember: '#1E1E2F',
        ash: '#2C2C3C',
        coal: '#101015',
        node: '#00F0FF',
      },
      fontFamily: {
        mono: ['JetBrains Mono', 'monospace'],
        flame: ['"Share Tech Mono"', 'monospace'],
      },
    },
  },
  plugins: [],
} satisfies Config;
