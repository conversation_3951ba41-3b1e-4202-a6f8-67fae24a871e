import Anthropic from '@anthropic-ai/sdk';

// Initialize the Anthropic client with API key from environment variables
const anthropic = new Anthropic({
  apiKey: process.env.CLAUDE_API_KEY || 'your-claude-api-key',
});

export default anthropic;

// Function to generate a chat completion using Claude Opus 4
export async function generateChatCompletion(
  messages: { role: 'user' | 'assistant' | 'system'; content: string }[],
  model: string = 'claude-3-opus-20240229'
) {
  try {
    // Convert messages format for Claude API
    const systemMessage = messages.find(msg => msg.role === 'system');
    const conversationMessages = messages.filter(msg => msg.role !== 'system');

    const response = await anthropic.messages.create({
      model,
      max_tokens: 1024,
      system: systemMessage?.content,
      messages: conversationMessages.map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
      })),
      temperature: 0.7,
    });

    return response.content[0]?.type === 'text' ? response.content[0].text : 'No response from <PERSON>.';
  } catch (error) {
    console.error('Error generating chat completion:', error);
    throw error;
  }
}

// Function to send a simple prompt to <PERSON> <PERSON>
export async function sendToClaudeOpus(prompt: string) {
  try {
    const response = await anthropic.messages.create({
      model: 'claude-3-opus-20240229',
      max_tokens: 1024,
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
    });

    return response.content[0]?.type === 'text' ? response.content[0].text : 'No response from Claude.';
  } catch (error) {
    console.error('Error sending to Claude Opus:', error);
    throw error;
  }
}
