import type { Metada<PERSON> } from "next";
import { JetBrains_Mono, Share_Tech_Mono } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/components/AuthContext";

const jetBrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
});

const shareTechMono = Share_Tech_Mono({
  variable: "--font-share-tech-mono",
  subsets: ["latin"],
  weight: "400",
});

export const metadata: Metadata = {
  title: "Hocus Opus - AI Chat with Claude",
  description: "A production-ready, open-source AI chat template using Next.js, Supabase, and Claude Opus 4",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${jetBrainsMono.variable} ${shareTechMono.variable} antialiased bg-ember min-h-screen text-white font-mono`}
      >
        <AuthProvider>
          {children}
        </AuthProvider>
        <div id="NODE_SEAL" className="hidden">
          <span className="ghostfire-sigil">NODE Seal v1.0</span>
        </div>
      </body>
    </html>
  );
}
