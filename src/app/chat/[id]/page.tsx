'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthContext';
import ChatWindow from '@/components/ChatWindow';
import Link from 'next/link';

export default function ChatPage({ params }: { params: Promise<{ id: string }> }) {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [chatId, setChatId] = useState<string | null>(null);

  useEffect(() => {
    params.then((resolvedParams) => {
      setChatId(resolvedParams.id);
    });
  }, [params]);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-ember">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-flame"></div>
      </div>
    );
  }

  if (!user || !chatId) {
    return null; // Will redirect in useEffect or still loading chatId
  }

  return (
    <div className="min-h-screen flex flex-col bg-ember">
      <header className="bg-coal shadow-sm border-b border-ash">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <Link href="/" className="text-gray-300 hover:text-white mr-4 transition-colors">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </Link>
            <h1 className="text-2xl font-bold flame-text font-flame">Hocus Opus</h1>
          </div>
        </div>
      </header>

      <main className="flex-1 max-w-7xl mx-auto w-full px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-ash shadow rounded-lg overflow-hidden h-[calc(100vh-12rem)] border border-gray-600">
          <ChatWindow chatId={chatId} />
        </div>
      </main>

      <footer className="bg-coal border-t border-ash py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-sm text-gray-400">
          <p>
            Hocus Opus - A production-ready, open-source AI chat template using
            Next.js, Supabase, and Claude Opus 4
          </p>
          <p className="mt-1">
            Licensed under the{' '}
            <a
              href="https://thewitnesshall.com"
              target="_blank"
              rel="noopener noreferrer"
              className="flame-text hover:text-orange-400 transition-colors"
            >
              Flame Public Use License v1.0
            </a>
          </p>
        </div>
      </footer>
    </div>
  );
}
