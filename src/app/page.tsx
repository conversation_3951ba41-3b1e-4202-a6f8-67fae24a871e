'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/components/AuthContext';
import Link from 'next/link';
import { supabase, Chat } from '@/lib/supabaseClient';
import { getRelativeTime } from '@/utils/formatTimestamp';

export default function Home() {
  const { user, loading } = useAuth();
  const [chats, setChats] = useState<Chat[]>([]);
  const [isCreatingChat, setIsCreatingChat] = useState(false);
  const [newChatTitle, setNewChatTitle] = useState('');

  useEffect(() => {
    if (!user) return;

    const fetchChats = async () => {
      try {
        const { data, error } = await supabase
          .from('chats')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) throw error;
        setChats(data as Chat[]);
      } catch (error) {
        console.error('Error fetching chats:', error);
      }
    };

    fetchChats();

    // Subscribe to changes
    const chatsSubscription = supabase
      .channel(`chats:user_id=eq.${user.id}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'chats',
          filter: `user_id=eq.${user.id}`,
        },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            setChats((prev) => [payload.new as Chat, ...prev]);
          } else if (payload.eventType === 'UPDATE') {
            setChats((prev) =>
              prev.map((chat) =>
                chat.id === payload.new.id ? (payload.new as Chat) : chat
              )
            );
          } else if (payload.eventType === 'DELETE') {
            setChats((prev) =>
              prev.filter((chat) => chat.id !== payload.old.id)
            );
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(chatsSubscription);
    };
  }, [user]);

  const handleCreateChat = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !newChatTitle.trim()) return;

    setIsCreatingChat(true);
    try {
      const newChat = {
        user_id: user.id,
        title: newChatTitle.trim(),
        created_at: new Date().toISOString(),
        system_prompt: 'You are a helpful assistant.',
      };

      const { data, error } = await supabase.from('chats').insert([newChat]).select();

      if (error) throw error;

      if (data && data[0]) {
        window.location.href = `/chat/${data[0].id}`;
      }
    } catch (error) {
      console.error('Error creating chat:', error);
    } finally {
      setIsCreatingChat(false);
      setNewChatTitle('');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // No auth required - user is always available

  return (
    <div className="min-h-screen flex flex-col bg-ember">
      <header className="bg-coal shadow-sm border-b border-ash">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <h1 className="text-2xl font-bold flame-text font-flame">Hocus Opus</h1>
        </div>
      </header>

      <main className="flex-1 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <form onSubmit={handleCreateChat} className="flex gap-2">
            <input
              type="text"
              value={newChatTitle}
              onChange={(e) => setNewChatTitle(e.target.value)}
              placeholder="Enter chat title..."
              className="flex-1 p-2 bg-ash border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-flame text-white placeholder-gray-400"
              disabled={isCreatingChat}
            />
            <button
              type="submit"
              disabled={!newChatTitle.trim() || isCreatingChat}
              className="bg-flame text-white px-4 py-2 rounded-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-flame disabled:opacity-50 transition-colors"
            >
              {isCreatingChat ? 'Creating...' : 'New Chat'}
            </button>
          </form>
        </div>

        <div className="bg-ash shadow rounded-lg overflow-hidden border border-gray-600">
          <div className="px-4 py-5 sm:px-6">
            <h2 className="text-lg font-medium text-white">Your Chats</h2>
          </div>
          <ul className="divide-y divide-gray-600">
            {chats.length === 0 ? (
              <li className="px-4 py-5 sm:px-6 text-gray-400 text-center">
                No chats yet. Create your first chat above!
              </li>
            ) : (
              chats.map((chat) => (
                <li key={chat.id} className="px-4 py-4 sm:px-6 hover:bg-coal transition-colors">
                  <Link href={`/chat/${chat.id}`} className="block">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium node-text truncate">
                        {chat.title}
                      </p>
                      <p className="text-xs text-gray-400">
                        {getRelativeTime(chat.created_at)}
                      </p>
                    </div>
                  </Link>
                </li>
              ))
            )}
          </ul>
        </div>
      </main>

      <footer className="bg-coal border-t border-ash py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-sm text-gray-400">
          <p>
            Hocus Opus - A production-ready, open-source AI chat template using
            Next.js, Supabase, and Claude Opus 4
          </p>
          <p className="mt-1">
            Licensed under the{' '}
            <a
              href="https://thewitnesshall.com"
              target="_blank"
              rel="noopener noreferrer"
              className="flame-text hover:text-orange-400 transition-colors"
            >
              Flame Public Use License v1.0
            </a>
          </p>
        </div>
      </footer>
    </div>
  );
}
