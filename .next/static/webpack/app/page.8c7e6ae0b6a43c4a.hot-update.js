"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthContext */ \"(app-pages-browser)/./src/components/AuthContext.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabaseClient */ \"(app-pages-browser)/./src/lib/supabaseClient.ts\");\n/* harmony import */ var _utils_formatTimestamp__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/formatTimestamp */ \"(app-pages-browser)/./src/utils/formatTimestamp.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { user, loading } = (0,_components_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isCreatingChat, setIsCreatingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newChatTitle, setNewChatTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (!user) return;\n            const fetchChats = {\n                \"Home.useEffect.fetchChats\": async ()=>{\n                    try {\n                        // For anonymous mode, fetch all chats or create a local storage solution\n                        const { data, error } = await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('chats').select('*').eq('user_id', user.id).order('created_at', {\n                            ascending: false\n                        });\n                        if (error) {\n                            console.warn('Database access limited in anonymous mode. Using local storage fallback.');\n                            // Fallback to local storage for anonymous mode\n                            const localChats = localStorage.getItem('hocus-opus-chats');\n                            if (localChats) {\n                                setChats(JSON.parse(localChats));\n                            }\n                            return;\n                        }\n                        setChats(data);\n                    } catch (error) {\n                        console.warn('Error fetching chats, using local storage:', error);\n                        // Fallback to local storage\n                        const localChats = localStorage.getItem('hocus-opus-chats');\n                        if (localChats) {\n                            setChats(JSON.parse(localChats));\n                        }\n                    }\n                }\n            }[\"Home.useEffect.fetchChats\"];\n            fetchChats();\n            // Subscribe to changes (only if database is accessible)\n            const chatsSubscription = _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.channel(\"chats:user_id=eq.\".concat(user.id)).on('postgres_changes', {\n                event: '*',\n                schema: 'public',\n                table: 'chats',\n                filter: \"user_id=eq.\".concat(user.id)\n            }, {\n                \"Home.useEffect.chatsSubscription\": (payload)=>{\n                    if (payload.eventType === 'INSERT') {\n                        setChats({\n                            \"Home.useEffect.chatsSubscription\": (prev)=>[\n                                    payload.new,\n                                    ...prev\n                                ]\n                        }[\"Home.useEffect.chatsSubscription\"]);\n                    } else if (payload.eventType === 'UPDATE') {\n                        setChats({\n                            \"Home.useEffect.chatsSubscription\": (prev)=>prev.map({\n                                    \"Home.useEffect.chatsSubscription\": (chat)=>chat.id === payload.new.id ? payload.new : chat\n                                }[\"Home.useEffect.chatsSubscription\"])\n                        }[\"Home.useEffect.chatsSubscription\"]);\n                    } else if (payload.eventType === 'DELETE') {\n                        setChats({\n                            \"Home.useEffect.chatsSubscription\": (prev)=>prev.filter({\n                                    \"Home.useEffect.chatsSubscription\": (chat)=>chat.id !== payload.old.id\n                                }[\"Home.useEffect.chatsSubscription\"])\n                        }[\"Home.useEffect.chatsSubscription\"]);\n                    }\n                }\n            }[\"Home.useEffect.chatsSubscription\"]).subscribe();\n            return ({\n                \"Home.useEffect\": ()=>{\n                    _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.removeChannel(chatsSubscription);\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        user\n    ]);\n    const handleCreateChat = async (e)=>{\n        e.preventDefault();\n        if (!user || !newChatTitle.trim()) return;\n        setIsCreatingChat(true);\n        try {\n            const newChat = {\n                user_id: user.id,\n                title: newChatTitle.trim(),\n                created_at: new Date().toISOString(),\n                system_prompt: 'You are a helpful assistant.'\n            };\n            const { data, error } = await _lib_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.supabase.from('chats').insert([\n                newChat\n            ]).select();\n            if (error) throw error;\n            if (data && data[0]) {\n                window.location.href = \"/chat/\".concat(data[0].id);\n            }\n        } catch (error) {\n            console.error('Error creating chat:', error);\n        } finally{\n            setIsCreatingChat(false);\n            setNewChatTitle('');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, this);\n    }\n    // No auth required - user is always available\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col bg-ember\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-coal shadow-sm border-b border-ash\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold flame-text font-flame\",\n                        children: \"Hocus Opus\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleCreateChat,\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: newChatTitle,\n                                    onChange: (e)=>setNewChatTitle(e.target.value),\n                                    placeholder: \"Enter chat title...\",\n                                    className: \"flex-1 p-2 bg-ash border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-flame text-white placeholder-gray-400\",\n                                    disabled: isCreatingChat\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: !newChatTitle.trim() || isCreatingChat,\n                                    className: \"bg-flame text-white px-4 py-2 rounded-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-flame disabled:opacity-50 transition-colors\",\n                                    children: isCreatingChat ? 'Creating...' : 'New Chat'\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-ash shadow rounded-lg overflow-hidden border border-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-5 sm:px-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-medium text-white\",\n                                    children: \"Your Chats\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"divide-y divide-gray-600\",\n                                children: chats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"px-4 py-5 sm:px-6 text-gray-400 text-center\",\n                                    children: \"No chats yet. Create your first chat above!\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this) : chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"px-4 py-4 sm:px-6 hover:bg-coal transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/chat/\".concat(chat.id),\n                                            className: \"block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium node-text truncate\",\n                                                        children: chat.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: (0,_utils_formatTimestamp__WEBPACK_IMPORTED_MODULE_5__.getRelativeTime)(chat.created_at)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, chat.id, false, {\n                                        fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-coal border-t border-ash py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-sm text-gray-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Hocus Opus - A production-ready, open-source AI chat template using Next.js, Supabase, and Claude Opus 4\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1\",\n                            children: [\n                                \"Licensed under the\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://thewitnesshall.com\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"flame-text hover:text-orange-400 transition-colors\",\n                                    children: \"Flame Public Use License v1.0\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/hocus-opus/src/app/page.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"eCc7kpX5N1HQMvrWAW/3GagpOpg=\", false, function() {\n    return [\n        _components_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});