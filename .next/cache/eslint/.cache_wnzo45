[{"/home/<USER>/hocus-opus/src/app/api/chat/route.ts": "1", "/home/<USER>/hocus-opus/src/app/chat/[id]/page.tsx": "2", "/home/<USER>/hocus-opus/src/app/layout.tsx": "3", "/home/<USER>/hocus-opus/src/app/page.tsx": "4", "/home/<USER>/hocus-opus/src/components/AuthContext.tsx": "5", "/home/<USER>/hocus-opus/src/components/ChatWindow.tsx": "6", "/home/<USER>/hocus-opus/src/components/MessageBubble.tsx": "7", "/home/<USER>/hocus-opus/src/components/SystemPromptEditor.tsx": "8", "/home/<USER>/hocus-opus/src/lib/claude.ts": "9", "/home/<USER>/hocus-opus/src/lib/supabaseClient.ts": "10", "/home/<USER>/hocus-opus/src/utils/formatTimestamp.ts": "11"}, {"size": 1495, "mtime": 1748086916218, "results": "12", "hashOfConfig": "13"}, {"size": 2745, "mtime": 1748086882332, "results": "14", "hashOfConfig": "13"}, {"size": 1117, "mtime": 1748086439258, "results": "15", "hashOfConfig": "13"}, {"size": 6173, "mtime": 1748086859942, "results": "16", "hashOfConfig": "13"}, {"size": 1741, "mtime": 1748086995442, "results": "17", "hashOfConfig": "13"}, {"size": 6978, "mtime": 1748086110675, "results": "18", "hashOfConfig": "13"}, {"size": 894, "mtime": 1748086110675, "results": "19", "hashOfConfig": "13"}, {"size": 2279, "mtime": 1748086110675, "results": "20", "hashOfConfig": "13"}, {"size": 1747, "mtime": 1748086387306, "results": "21", "hashOfConfig": "13"}, {"size": 731, "mtime": 1748086110675, "results": "22", "hashOfConfig": "13"}, {"size": 1652, "mtime": 1748086110676, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "11oc0mv", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/hocus-opus/src/app/api/chat/route.ts", [], [], "/home/<USER>/hocus-opus/src/app/chat/[id]/page.tsx", [], [], "/home/<USER>/hocus-opus/src/app/layout.tsx", [], [], "/home/<USER>/hocus-opus/src/app/page.tsx", [], [], "/home/<USER>/hocus-opus/src/components/AuthContext.tsx", [], ["57", "58"], "/home/<USER>/hocus-opus/src/components/ChatWindow.tsx", [], [], "/home/<USER>/hocus-opus/src/components/MessageBubble.tsx", [], [], "/home/<USER>/hocus-opus/src/components/SystemPromptEditor.tsx", [], [], "/home/<USER>/hocus-opus/src/lib/claude.ts", [], [], "/home/<USER>/hocus-opus/src/lib/supabaseClient.ts", [], [], "/home/<USER>/hocus-opus/src/utils/formatTimestamp.ts", [], [], {"ruleId": "59", "severity": 2, "message": "60", "line": 34, "column": 28, "nodeType": null, "messageId": "61", "endLine": 34, "endColumn": 33, "suppressions": "62"}, {"ruleId": "59", "severity": 2, "message": "60", "line": 40, "column": 28, "nodeType": null, "messageId": "61", "endLine": 40, "endColumn": 33, "suppressions": "63"}, "@typescript-eslint/no-unused-vars", "'_args' is defined but never used.", "unusedVar", ["64"], ["65"], {"kind": "66", "justification": "67"}, {"kind": "66", "justification": "67"}, "directive", ""]